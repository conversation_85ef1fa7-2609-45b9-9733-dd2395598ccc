package com.ymx.common.utils;

import org.apache.commons.lang.ArrayUtils;
import org.apache.mina.core.future.ReadFuture;
import org.apache.mina.core.future.WriteFuture;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.Socket;
import java.nio.ByteBuffer;
import java.nio.channels.SocketChannel;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Locale;

public class ByteUtil {

	private static final Logger logger = LoggerFactory.getLogger(ByteUtil.class);


	/**
	 * 打印数据
	 * @param data 字节数组
	 */
	public static void printf( byte[] data ) {
		for (int i = 0; i < data.length; i++) {
			System.out.println(" RECEIVE  DATA >>>> [" + i + "]  " + data[i]  +  " 转成16进制 >> " + byteToHex(data[i]));
		}
		System.out.println("--------------------------------------------------");
	}

	/**
	 *
	 * @param str 数据
	 * @return
	 */
	public static byte generateByte(String str) {
		byte tmp = 0;
		char[] arr = str.toCharArray();
		if(arr.length > 2){
			return tmp;
		}
		int t0 = Integer.parseInt(Character.toString(arr[0]), 16);
		int t1 = Integer.parseInt(Character.toString(arr[1]), 16);
		byte tmp0 = (byte)t0;
		byte tmp1 = (byte)t1;
		tmp = (byte) (tmp0 << 4);
		tmp = (byte) (tmp | tmp1);
		return tmp;
	}


	/**
	 * String转byte数组
	 */
	public static byte[] hexStr2Byte(String hex) {
		ByteBuffer bf = ByteBuffer.allocate(hex.length() / 2);
		for (int i = 0;i < hex.length();i++) {
			String hexStr = hex.charAt(i) + "";
			i++;
			hexStr += hex.charAt(i);
			byte b = (byte) Integer.parseInt(hexStr, 16);
			bf.put(b);
		}
		return bf.array();
	}

	/**
	 * 方法描述：数字前导填零
	 * @param num 待填零的数字 width 需要的数字宽度
	 * @return 填零后的数字字符串形式
	 */
	public static String getPreFillNum(String num, int width) {
		StringBuilder strNum = new StringBuilder(num);
		int len = strNum.length();
		if (width - len <= 0) {
			return String.valueOf(num);
		}
		for (int i = 0;i < width - len;i++) {
			strNum.insert(0, "0");
		}
		return strNum.toString();
	}

	/**
	 * 功能描述：byte转16进制
	 */
	public static String byteToHex(byte b) {
		String hex = Integer.toHexString(b & 0xFF);
		if (hex.length() == 1) {
			hex = '0' + hex;
		}
		return hex.toUpperCase(Locale.getDefault());
	}

	/**
	 * 发送数据并且接收数据
	 * @param session  socket 会话
	 * @param content  发送字节数组内容
//	 * @param async  true 异步 false 同步
	 * @return byte[]
	 */
	public static byte[] getDataByteArray(IoSession session , byte[] content) {
		session.getConfig().setUseReadOperation(true);
//		session.getConfig().setReadBufferSize(80);
//		((SocketSessionConfig)session.getConfig()).setReceiveBufferSize(80);
		// 接收数据
		byte[] respone = null;
		// 发送数据
		WriteFuture writeFuture = session.write(content);
//		writeFuture.awaitUninterruptibly();
		if (null != writeFuture.getException()) {
			session.getConfig().setUseReadOperation(false);
			return respone;
		}
		if(writeFuture.isWritten()){
			ReadFuture readFuture = session.read();
//			readFuture.awaitUninterruptibly();
			if (null != readFuture.getException()) {
				session.getConfig().setUseReadOperation(false);
				return respone;
			}
			if (readFuture.isRead()) {
				respone = (byte[]) readFuture.getMessage();
			}
		}
		session.getConfig().setUseReadOperation(false);
		return respone;
	}

	/**
	 * 加载数据
	 * @param  length 数组长度
	 * @param  data   数组初始化数据
	 * @return byte[] 字节数组
	 */
	public static byte[] loadData( int length , byte ... data ) {
		byte[] bytes = new byte[ length ];
		if (null ==  data) {
			return bytes;
		}
		if (length < data.length) {
			throw new ArrayIndexOutOfBoundsException("【数据超出数组的长度】");
		}
		for (byte i = 0; i < length; i++) {
			if (i ==  data.length) {
				break;
			}
			bytes[i] = data[i];
		}
		return bytes;
	}

	/**
	 * byte数组中取int数值，本方法适用于(低位在前，高位在后)的顺序，和和 intToBytes（）配套使用
	 * @param src      byte数组
	 * @param offset  从数组的第offset位开始
	 * @return int数值
	 */
	public static int bytesToInt(byte[] src, int offset) {
		int value;
		// &0xFF变为一个正数,<< 左移变大,低位补0，或运算拼接高低位
		value = (int) ((src[offset] & 0xFF)
				| ((src[offset+1] & 0xFF)<<8)
				| ((src[offset+2] & 0xFF)<<16)
				| ((src[offset+3] & 0xFF)<<24));
		return value;
	}

	//温度转换 温度可能为负值 表示零下
	public static int bytesToIntForTemp(byte[] src, int offset) {
		int value;
		value = src[offset] | (src[offset+1] <<8);
		return value;
	}

	public static int OneByteForTemp(byte[] src, int offset) {
		int value;
		value = src[offset];
		return value;
	}


	// 保留低位，把byte数组转为两位正整数
	public static int twoBytesToInt(byte[] src, int offset) {
		int value;
		value = ((src[offset] & 0xFF)
				| ((src[offset+1] & 0xFF)<<8));
		return value;
	}

	public static int oneByteToInt(byte[] src, int offset) {
		int value;
		value = ((src[offset] & 0xFF));
		return value;
	}

	/**
	 * byte数组中取int数值，本方法适用于(低位在后，高位在前)的顺序。和intToBytes2（）配套使用
	 */
	public static int bytesToInt2(byte[] src, int offset) {
		int value;
		value = (int) ( ((src[offset] & 0xFF)<<24)
				|((src[offset+1] & 0xFF)<<16)
				|((src[offset+2] & 0xFF)<<8)
				|(src[offset+3] & 0xFF));
		return value;
	}

	/**
	 * 将int数值转换为占四个字节的byte数组，本方法适用于(高位在前，低位在后)的顺序。  和bytesToInt2（）配套使用
	 */
	public static byte[] intToBytes2(int value)  {
		byte[] src = new byte[4];
		src[0] = (byte) ((value>>24) & 0xFF);
		src[1] = (byte) ((value>>16)& 0xFF);
		src[2] = (byte) ((value>>8)&0xFF);
		src[3] = (byte) (value & 0xFF);
		return src;
	}

	/**
	 * 将int数值转换为占四个字节的byte数组，本方法适用于(低位在前，高位在后)的顺序。 和bytesToInt（）配套使用
	 * @param value  要转换的int值
	 * @return byte数组
	 */
	public static byte[] intToBytes( int value ) {
		byte[] src = new byte[4];
		src[3] =  (byte) ((value>>24) & 0xFF);
		src[2] =  (byte) ((value>>16) & 0xFF);
		src[1] =  (byte) ((value>>8) & 0xFF);
		src[0] =  (byte) (value & 0xFF);
		return src;
	}

	/**
	 * 将int数值转换为占len个字节的byte数组，本方法适用于(低位在前，高位在后 小端字节序)的顺序
	 * @param value 要转换的int值
	 * @param len  转换的byte数组的长度
	 * @return byte数组
	 */
	public static byte[] int2Bytes(int value, int len) {
		byte[] b = new byte[len];
		for (int i = 0; i < len; i++) {
			b[i] = (byte)((value >> 8 * i) & 0xff);
		}
		return b;
	}

	/**
	 * 多个字节数组合并
	 * @param values 数组
	 * @return
	 */
	public static byte[] byteMergerAll( byte[] ... values ) {
		int length_byte = 0;
		for (int i = 0; i < values.length; i++) {
			length_byte += values[i].length;
		}
		byte[] all_byte = new byte[ length_byte ];
		int countLength = 0;
		for (int i = 0; i < values.length; i++) {
			byte[] b = values[i];
			System.arraycopy(b, 0, all_byte, countLength, b.length);
			countLength += b.length;
		}
		return all_byte;
	}

	/**
	 * 16进制字符串转字符串
	 */
	public static String hex2String(String hex) throws Exception{
		String r = bytes2String(hexString2Bytes(hex));
		return r;
	}





	/**
	 * 将字节数组转换成16进制字符串
	 * @param array   需要转换的字符串
	 * @param toPrint 是否为了打印输出，如果为true则会每2位自己添加一个空格
	 * @return 转换完成的字符串
	 */
	public static String byteArrayToHexString(byte[] array, boolean toPrint) {
		if (array == null || array.length <= 0) {
			return "null";
		}
		StringBuilder sb = new StringBuilder();
		for (byte anArray : array) {
			sb.append(byteToHex(anArray));
			if (toPrint) {
				sb.append(" ");
			}
		}
		return sb.toString();
	}

	/* Convert byte[] to hex string.这里我们可以将byte转换成int，然后利用Integer.toHexString(int)来转换成16进制字符串。
    * @param src byte[] data
    * @return hex string
    */
	public static String bytesToHexString(byte[] src){
		StringBuilder stringBuilder = new StringBuilder("");
		if (src == null || src.length <= 0) {
			return null;
		}
		for (byte b : src) {
			int v = b & 0xFF;
			String hv = Integer.toHexString(v);
			if (hv.length() < 2) {
				stringBuilder.append(0);
			}
			stringBuilder.append(hv);
		}
		return stringBuilder.toString();
	}

	public static ArrayList<String> bytesToHexList(byte[] src){
		if (src == null || src.length == 0) {
			return null;
		}
		ArrayList<String> hexList=new ArrayList<>();
		for (byte b : src) {
			int v = b & 0xFF;
			String hv = Integer.toHexString(v);
			StringBuilder stringBuilder = new StringBuilder();
			if (hv.length() < 2) {
				stringBuilder.append(0);
			}
			stringBuilder.append(hv);
			hexList.add(stringBuilder.toString());
		}
		return hexList;
	}


	public static byte[] hexStringToBytes(String hexString) {
		if (hexString == null || hexString.equals("")) {
			return null;
		}
		hexString = hexString.toUpperCase();
		int length = hexString.length() / 2;
		char[] hexChars = hexString.toCharArray();
		byte[] d = new byte[length];
		for (int i = 0; i < length; i++) {
			int pos = i * 2;
			d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
		}
		return d;
	}

	/**
     * 16进制字符串转字节数组
     */
	public static byte[] hexString2Bytes(String hex) {
		if ((hex == null) || (hex.equals(""))){
			return null;
		}
		else if (hex.length()%2 != 0){
			return null;
		}
		else{
			// 字符串转为大写模式
			hex = hex.toUpperCase();
			// 获取字符串一半的长度
			int len = hex.length()/2;
			// 新建一个byte数组
			byte[] b = new byte[len];
			// 字符串转为字符数组
			char[] hc = hex.toCharArray();
			// 循环字符数组
			for (int i=0; i<len; i++){
				// 下标变为2倍
				int p=2*i;
				// 字符转byte 再左移4个 和下一个字符转byte 或运算 最后结果转byte
				// 左移4个去掉高位
				b[i] = (byte) (charToByte(hc[p]) << 4 | charToByte(hc[p+1]));
			}
			return b;
		}
	}

	/**
     * 字节数组转字符串
     */
	public static String bytes2String(byte[] b)  {
		String r = null;
		try {
			r = new String (b,"UTF-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return r;
	}

	/**
     * 字符转换为字节
     */
	private static byte charToByte(char c) {
		return (byte) "0123456789ABCDEF".indexOf(c);
	}

	/**
	 * 字符串转字节数组
	 */
	public static byte[] string2Bytes(String s){
		byte[] r = s.getBytes();
		return r;
	}

	/**
	 * 位转成字节
	 * @param str
	 * @return
	 */
	public static byte bitStringToByte(String str) {
		if(null == str){
			throw new RuntimeException("when bit string convert to byte, Object can not be null!");
		}
		if (8 != str.length()){
			throw new RuntimeException("bit string'length must be 8");
		}
		try{
			//判断最高位，决定正负
			if(str.charAt(0) == '0'){
				return (byte) Integer.parseInt(str,2);
			}else if(str.charAt(0) == '1'){
				return (byte) (Integer.parseInt(str,2) - 256);
			}
		}catch (NumberFormatException e){
			throw new RuntimeException("bit string convert to byte failed, byte String must only include 0 and 1!");
		}
		return 0;
	}

	/**
	 * 根据value字符串 获取length长度 字节数组
	 * @param value  字符串
	 * @param length 字节数组长度
	 * @return
	 */
	public static byte[] getByteArrayByValue(String value , int length ){
		byte[] bytes = new byte[length];
		for (int i = 0 ; i < length ; i++ ) {
//			if ( length  == (i + 1) ) {
//				break;
//			}
			bytes[i] =  (byte) Integer.parseInt(value.charAt(i) + "");
		}
		return bytes;
	}

	/**
	 * 根据字节数组 将字节数组元素拼结成字符串
	 * @param data 字节数组
	 * @param isSpace 是否带空格间隔 true , false
	 * @return
	 */
	public static String getStringByByteArrays(byte[] data , boolean isSpace) {
		StringBuilder sb  = new StringBuilder();
		if (null != data && data.length > 0) {
			for (int i = 0; i < data.length; i++) {
				sb.append(data[i]);
				if ( isSpace && (i != data.length - 1 )) {
					sb.append(" ");
				}
			}
		}
		return sb.toString();
	}

	// 转换任务id 允许中间带0
	public static String getStringWithZero(byte[] data) {
		if (data == null) {
			return "";
		}
		StringBuilder stb = new StringBuilder();
		/// 数组的第一位保留原有长度
		stb.append(data[0]);
		/// %02d 的含义是将一个整数格式化为两位宽度的十进制数，不足两位时在左侧用零进行填充
		for (int i = 1; i < data.length; i++) {
			stb.append(String.format("%02d", data[i]));
		}
		return stb.toString();
	}

	public static String getHexChipId(byte[] chipIdByte)
	{
		String  chipId=ByteUtil.bytesToHexString(chipIdByte);
		if (chipId != null) {
//			logger.info("chipId:{}", chipId);
			// 如果chipId第一个字符是0 就把0去掉
			while (chipId.startsWith("0")) {
				chipId = chipId.substring(1);
			}
		} else {
			logger.info("chipId is null");
		}
		return chipId;
	}

	public static String getStringByChipIdByteArrays(byte[] data , boolean isSpace) {
		StringBuilder sb  = new StringBuilder();
		if (null != data && data.length > 0) {
			// 最后两位小于10补0  前面的是多少就是多少
			for (int i = 0; i < data.length; i++) {
				if (i == (data.length - 1)) {
					sb.append(data[i] < 10 ? "0" + data[i] : data[i]);
				} else if (i == (data.length - 2)) {
					sb.append(data[i] < 10 ? "0" + data[i] : data[i]);
				}else {
					sb.append(data[i]);
				}
				// 如果isSpace为true 在每个字符中间补空格
				if (isSpace && (i != data.length - 1)) {
					sb.append(" ");
				}
			}
		}
		return sb.toString();
	}

	/**
	 * @param chipId   组件id字节处理
	 * @param length   字节数组长度
	 * @return
	 */
	public static byte[] getByteChipId(String chipId , int length ){
		byte[] bytes = new byte[ length ];
		if(chipId!=null){
			if(chipId.length()<10){
				chipId=getChipId(chipId);
			}
			//两个字符对应一个字节
			bytes[0] = (byte)Integer.parseInt(chipId.substring(chipId.length()-10,chipId.length()-8));
			bytes[1] = (byte)Integer.parseInt(chipId.substring(chipId.length()-8,chipId.length()-6));
			bytes[2] = (byte)Integer.parseInt(chipId.substring(chipId.length()-6,chipId.length()-4));
			bytes[3] = (byte)Integer.parseInt(chipId.substring(chipId.length()-4,chipId.length()-2));
			bytes[4] = (byte)Integer.parseInt(chipId.substring(chipId.length()-2));
		}
		return bytes;
	}
	public static String getChipId(String chipId){
		while (chipId.length()<10){
			chipId="0"+chipId;
		}
		return chipId;
	}

	public static String getChipIdStr(byte[] chipIdArray)
	{
		String chipIdStr;
		if (chipIdArray[0] == 0 && chipIdArray[1] == 0 && chipIdArray[2] <= 1) {
			chipIdStr = ByteUtil.getStringByChipIdByteArrays(chipIdArray, false);
		} else {
			chipIdStr = ByteUtil.getHexChipId(chipIdArray);
		}
		return chipIdStr;
	}

	public static byte[] getByteArrayByString(String taskId , int length ) {
		byte[] bytes = new byte[length];

		if (taskId != null) {
			int needLen = 2 * length;
			// 如果任务id长度不够，就在前面补0
			if (taskId.length() < needLen) {
				while (taskId.length() < needLen) {
					taskId = "0" + taskId;
				}
			}
			for (int i = 0; i < length; i++) {
				if (i + 1 != length) {
					// 不是最后一位 就截取两个字符
					bytes[i] = (byte) Integer.parseInt(taskId.substring(2 * i, 2 * (i + 1)));
				} else {
					// 最后一位截取最后的字符
					bytes[i] = (byte) Integer.parseInt(taskId.substring(2 * i));
				}
			}
		}
		return bytes;
	}

	public static String getStringByByteArray(byte[] data) {
		return getStringByChipIdByteArrays(data,false);
	}

	/**
	 * 接收数据
	 */
	public static byte[] receiveMsg(Socket socket){
		// 接收数据
		try {
			DataInputStream inputStream = new DataInputStream(socket.getInputStream());
			byte[] data = new byte[80];
			inputStream.read(data);
			return  data;
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 发送数据
	 * @param response 响应字节数组
	 * @param out      输出流
	 */
	public static void serverToClientResponse( byte[] response , OutputStream out ){
		try {
			// 应答客户端
			out.write(response);
			out.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public static void sendByteArrayData(IoSession session, byte[] bytes ) {
		ByteBuffer buffer = ByteBuffer.wrap(bytes);
		session.write(buffer);
	}

	public static byte[] receiveDataSession(IoSession session) {
		ReadFuture readFuture = session.read();
		ReadFuture rf = session.read();
		return (byte[]) rf.getMessage();
	}


	public static void sendByteArrayData(SocketChannel socketChannel, byte[] bytes ) {
		ByteBuffer buffer = ByteBuffer.wrap(bytes);
		try {
			socketChannel.write(buffer);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public static Object receiveDataSocketChannel(SocketChannel socketChannel) {
		Object obj = null;
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		ByteBuffer intBuffer = ByteBuffer.allocate(4);
		ByteBuffer objBuffer = ByteBuffer.allocate(1024);
		int size = 0;
		int sum = 0;
		int objlen = 0;
		byte[] bytes = null;
		try {
			while ((size = socketChannel.read(intBuffer)) > 0) {
				intBuffer.flip();
				bytes = new byte[size];
				intBuffer.get(bytes);
				baos.write(bytes);
				intBuffer.clear();
				if (bytes.length == 4) {
					objlen = bytesToInt(bytes, 0);
				}
				if (objlen > 0) {
					byte[] objByte = new byte[0];
					while (sum != objlen) {
						size = socketChannel.read(objBuffer);
						if (size > 0) {
							objBuffer.flip();
							bytes = new byte[size];
							objBuffer.get(bytes, 0, size);
							baos.write(bytes);
							objBuffer.clear();
							objByte = ArrayUtils.addAll(objByte, bytes);
							sum += bytes.length;
						}
					}
					obj = ByteToObject(objByte);
					break;
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				baos.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return obj;
	}

	public static Object ByteToObject(byte[] bytes) {
		Object obj = null;
		try {
			// bytearray to object
			ByteArrayInputStream bi = new ByteArrayInputStream(bytes);
			ObjectInputStream oi = new ObjectInputStream(bi);
			obj = oi.readObject();
			bi.close();
			oi.close();
		} catch (Exception e) {
			//e.printStackTrace();
		}
		return obj;
	}

	public static byte[] ObjectToByte(Object obj) {
		byte[] bytes = null;
		try {
			// object to bytearray
			ByteArrayOutputStream bo = new ByteArrayOutputStream();
			ObjectOutputStream oo = new ObjectOutputStream(bo);
			oo.writeObject(obj);
			bytes = bo.toByteArray();
			bo.close();
			oo.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return bytes;
	}


	/**
	 *
	 */
	public static class BUS_VALUE {
		/** 信息收集响应 */
		public static final byte BUS_01 = 1;
		/** 异常警报  */
		public static final byte BUS_02= 2;
		/** 表示远端遥控结果 */
		public static final byte BUS_03 = 0x3;
		/** 组件鉴权 */
		public static final byte BUS_04 = 4;
		/** 表示异常解除 */
		public static final byte BUS_08 = 8;
		/** 自动打开或关闭上报 (温度过高 自动关闭) */
		public static final byte BUS_09 = 0x9;
		/** 注册 */
		public static final byte BUS_100 = 100;//0x64
		/** 心跳包  */
		public static final byte BUS_101 = 101;//0x65

		/** 远端修改参数  */
		public static final byte BUS_0a = 0xa;//0xa
		/** 版本下发申请  */
		public static final byte BUS_70 = 70;//0x70
		/** 版本下发  */
		public static final byte BUS_71 = 71;//0x71
		/** 采集器下发版本并升级  */
		public static final byte BUS_112 = 112;//0x70
		/** 中继器，优化器下发版本  */
		public static final byte BUS_113 = 113;//0x71
		/** 采集器版本查询请求  */
		public static final byte BUS_114 = 114;//0x72
		/** 中继器升级命令  */
		public static final byte BUS_48 = 48;//0x30
		/** 中继器固件加载  */
		public static final byte BUS_49 = 49;//0x31
		/** 中继器版本查询  */
		public static final byte BUS_50 = 50;//0x32
		/** 优化器升级命令  */
		public static final byte BUS_16 = 16;//0x10
		/** 优化器版本查询  */
		public static final byte BUS_18 = 18;//0x12
		/** 逆变器数据查询  */
		public static final byte BUS_90 = 90;//0x90
		/** 逆变器数据查询  */
		public static final byte BUS_91 = 91;//0x91
		/** 手机端cmd */
		public static final byte BUS_106 = 106;

	}

	public static class Constants {
		/** 打开 */
		public static final Integer ISOPEN_1 = 1;
		/** 关闭 */
		public static final Integer ISOPEN_2 = 2;

	}


	private static final int POLYNOMIAL = 0x1021;
	private static final int INITIAL_VALUE = 0xFFFF;

	public static byte[] calculateCRC(byte[] bytes) {

		int crc = INITIAL_VALUE;
		for (byte b : bytes) {
			crc ^= b << 8;
			for (int i = 0; i < 8; i++) {
				if ((crc & 0x8000) != 0) {
					crc = (crc << 1) ^ POLYNOMIAL;
				} else {
					crc <<= 1;
				}
			}
		}

		return int2Bytes(crc,2);
	}

	public static byte[] copyOfRange(byte[] original, int from, int to) {
		int length = to - from;
		byte[] result = new byte[length];
		System.arraycopy(original, from, result, 0, length);
		return result;
	}


	/**
	 * 根据偏移量在原基础数组中 获取偏移量数组
	 * @param srcByte   源数组
	 * @param srcBegin  源数组要复制的起始位置
	 * @param desBegin  数组的起始位
	 * @param length    复制的长度
	 * @return 返回目标数组
	 */
	public static byte[] getByteOffset(byte[] srcByte , int srcBegin , int desBegin  , int length ){
		// 数组
		byte[] bytes = new byte[ length ];
		// srcByte表示源数组，srcBegin表示源数组要复制的起始位置，bytes表示目标数组，desBegin目标数组的起始位, length表示要复制的长度
		System.arraycopy( srcByte , srcBegin , bytes , desBegin , length );
		return bytes;
	}
	/**
	 * 复制原数组范围内 返回新数组
	 * @param srcByte   源数组
	 * @param from     起始位
	 * @param to       结束位
	 * @return 返回目标数组
	 */
	public static byte[] getByteOffset(byte[] srcByte , int from  , int to ){
		// 数组
		return  Arrays.copyOfRange( srcByte , from , to );
	}

	public static int crcSum(byte[] data)
	{
		int sum=0;
		int len=data.length;
		for (int i = 0; i <len ; i= i+2) {
			sum+= (((data[i]<<8) & 0xff00)+(data[i+1]& 0xff));
		}

		while ((sum&0xffff0000)!=0)
		{
			sum= (sum>>16) + (sum&0xffff);
		}

		sum= ((-1*(sum +1))&0xffff);
		return sum;
	}



}
