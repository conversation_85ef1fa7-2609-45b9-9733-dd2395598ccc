package com.ymx.common.common.constant;

import java.util.HashMap;
import java.util.Map;

public class ByteConstant {
     //信息收集响应
    public static final byte CMD_01_COLLECT = 1;

    public static final byte CMD_102_NEW_COLLECT = 102;// 0x66
    //异常警报
    public static final byte CMD_02_ABNORMAL_ALARM = 2;
    //远端遥控结果
    public static final byte CMD_03_REMOTE_CONTROL = 0x3;
    //组件鉴权
    public static final byte CMD_04_COMPONENT_AUTH = 4;
    //表示异常解除
    public static final byte CMD_08_ABNORMAL_RESTORE = 8;
    //自动打开或关闭上报 (温度过高 自动关闭)
    public static final byte CMD_09_OPEN_CLOSE = 0x9;
    //注册
    public static final byte CMD_100_REGISTER = 100;//0x64
    //心跳包
    public static final byte CMD_101_HEARTBEAT = 101;//0x65
    //远端修改参数
    public static final byte CMD_0A_REMOTE_ALTER = 0xa;//0xa
    //版本下发申请
    public static final byte CMD_70_VERSION_APPLY = 70;//0x70
    //版本下发
    public static final byte CMD_71_VERSION_SEND = 71;//0x71
    //采集器下发版本并升级
    public static final byte CMD_112_COLLECTOR_UPDATE = 112;//0x70
    //中继器，优化器下发版本
    public static final byte CMD_113_RELAY_OPTIMIZER_SEND = 113;//0x71
    //采集器版本查询请求
    public static final byte CMD_114_COLLECTOR_QUERY = 114;//0x72
    //中继器升级命令
    public static final byte CMD_48_RELAY_UPDATE = 48;//0x30
    //中继器固件加载
    public static final byte CMD_49_RELAY_LOAD = 49;//0x31
    //中继器版本查询
    public static final byte CMD_50_RELAY_QUERY = 50;//0x32
    //优化器升级命令
    public static final byte CMD_16_OPTIMIZER_UPDATE = 16;//0x10
    //优化器版本查询
    public static final byte CMD_18_OPTIMIZER_QUERY = 18;//0x12
    //逆变器数据查询
    public static final byte CMD_90_INVERTER_QUERY = 90;//0x90
    //逆变器组串数据查询
    public static final byte CMD_91_INVERTER_GROUP_QUERY = 91;//0x91
    //手机端cmd
    public static final byte CMD_106_PHONE = 106;
    // 按组串上传优化器信息
    public static final byte CMD_120_GROUP_OP_META = 120;
    // 按组串采集信息
    public static final byte CMD_121_GROUP_COLLECT = 121;
    // 返回成功的状态码
    public static final byte SUCCESS = 1;
    // 返回失败的状态码
    public static final byte FAIL = 2;


    public static Map<Byte, String> meaningMap;

    static {
        meaningMap = new HashMap<>();
        // 初始化map
        meaningMap.put(CMD_01_COLLECT,"collect");
        meaningMap.put(CMD_102_NEW_COLLECT,"new collect");
        meaningMap.put(CMD_02_ABNORMAL_ALARM,"abnormal alarm");
        meaningMap.put(CMD_03_REMOTE_CONTROL,"remote control");
        meaningMap.put(CMD_04_COMPONENT_AUTH,"component auth");
        meaningMap.put(CMD_08_ABNORMAL_RESTORE,"abnormal restore");
        meaningMap.put(CMD_09_OPEN_CLOSE,"open close");
        meaningMap.put(CMD_100_REGISTER,"register");
        meaningMap.put(CMD_101_HEARTBEAT,"heartbeat");
        meaningMap.put(CMD_0A_REMOTE_ALTER,"remote alter");
        meaningMap.put(CMD_70_VERSION_APPLY,"version apply");
        meaningMap.put(CMD_71_VERSION_SEND,"version send");
        meaningMap.put(CMD_112_COLLECTOR_UPDATE,"collector update");
        meaningMap.put(CMD_113_RELAY_OPTIMIZER_SEND,"relay optimizer send");
        meaningMap.put(CMD_114_COLLECTOR_QUERY,"collector query");
        meaningMap.put(CMD_48_RELAY_UPDATE,"relay update");
        meaningMap.put(CMD_49_RELAY_LOAD,"relay load");
        meaningMap.put(CMD_50_RELAY_QUERY,"relay query");
        meaningMap.put(CMD_16_OPTIMIZER_UPDATE,"optimizer update");
        meaningMap.put(CMD_18_OPTIMIZER_QUERY,"optimizer query");
        meaningMap.put(CMD_90_INVERTER_QUERY,"inverter query");
        meaningMap.put(CMD_91_INVERTER_GROUP_QUERY,"inverter group query");
        meaningMap.put(CMD_106_PHONE,"phone");
        meaningMap.put(CMD_120_GROUP_OP_META,"group op meta");
        meaningMap.put(CMD_121_GROUP_COLLECT,"group collect");
    }


}
