package com.ymx.socket.api.protocol.communication.mina.server.handler;

import com.ymx.common.common.constant.ByteConstant;
import com.ymx.common.utils.ByteUtil;
import com.ymx.service.cache.Configure;
import com.ymx.service.constant.ConstantsExt;
import com.ymx.service.photovoltaic.protocol.inter.DispatchService;
import com.ymx.service.photovoltaic.station.model.CrcProtocol;
import com.ymx.service.photovoltaic.station.model.SocketModel;
import com.ymx.service.photovoltaic.station.service.*;
import com.ymx.service.photovoltaic.zte.service.ProtocolDataService;
import com.ymx.socket.api.mqtt.MqttService;
import org.apache.commons.lang.ArrayUtils;
import org.apache.mina.core.future.WriteFuture;
import org.apache.mina.core.service.IoHandlerAdapter;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.InetSocketAddress;
import java.util.*;
import java.util.stream.Collectors;

@Component("serviceHandler")
public class ServiceHandler extends IoHandlerAdapter {

	private final Logger logger = LoggerFactory.getLogger(ServiceHandler.class);

	private static final String KEY_SESSION_CLIENT_IP = "KEY_SESSION_CLIENT_IP";

	private Map<IoSession, byte[]> map = new HashMap<>();

	private static final byte HEADER1_BYTE1 = -75; // 第一种包头的第一个字节
	private static final byte HEADER1_BYTE2 = 98; // 第一种包头的第二个字节
	private static final byte HEADER2_BYTE1 = -85; // 第二种包头的第一个字节
	private static final byte HEADER2_BYTE2 = -51; // 第二种包头的第二个字节
	private static final int OLD_PACKET_LENGTH = 80; // 第二种包头对应的包长

	// 协议数据服务层
	@Resource
	private ProtocolDataService protocolDataService;
	// 组件服务层
	@Resource
	private ComponentService componentService;
	// 组件采集服务层
	@Resource
	private ComponentCollectService componentCollectService;
	@Resource
	private ElectricalCollectService electricalCollectService;
	@Resource
	private VersionService versionService;
	@Resource
	private UpdateTaskService updateTaskService;
	@Resource
	private DispatchService dispatchService;

	@Autowired
	private MqttService mqttService;



	public void inputClosed(IoSession session) throws Exception {
		logger.info("inputClosed {}", session.getId());
		session.close(true);
	}


	@Override
	public void exceptionCaught(IoSession session, Throwable cause) {
		logger.error("exceptionCaught {} exception {} {}",session.getId(),cause.getMessage(),cause.getStackTrace());
	}


	@Override
	public void sessionCreated(IoSession session) {
		String clientIp = ((InetSocketAddress) session.getRemoteAddress()).getAddress().getHostAddress();
		session.setAttribute(KEY_SESSION_CLIENT_IP, clientIp);
		logger.info("sessionCreated {} clientIp {}",session.getId(),clientIp);
	}


	@Override
	public void sessionClosed(IoSession session) {
		logger.info("sessionClosed {}",session.getId());
		Configure.getMap().remove(session.getId() + "");
		// 连接关闭，清空存储收到信息的map
		map.remove(session);
	}


	@Override
	public void sessionOpened(IoSession session) {
		logger.info("sessionOpened {}",session.getId());
	}


	@Override
	public void messageReceived(final IoSession session, Object message) {

		// 这一次收到的字节
		byte[] nowReceiveArray = (byte[]) message;
		if (nowReceiveArray == null) {
			return;
		}

		InetSocketAddress localAddress = (InetSocketAddress) session.getLocalAddress();
		int localPort = localAddress.getPort();
		logger.debug("localPort {} received message {},length {}", localPort,
				nowReceiveArray, nowReceiveArray.length);
		// 获取session的所有未处理的字节
		byte[] noHandleArray = map.get(session);

		if (noHandleArray != null) {
			logger.debug("noHandleArray len {}",noHandleArray.length);
			// 把这次收到的包和原来的包合并
			noHandleArray = ArrayUtils.addAll(noHandleArray, nowReceiveArray);
		} else {
			noHandleArray = nowReceiveArray;
		}

		if (localPort == 5066) {
			handleOldProtocol(session, noHandleArray);
		} else if (localPort == 7066) {
			handleNewProtocol(session, noHandleArray);
		}
	}


	// 处理PLC采集器的包 进行新协议的分包
	private void handleNewProtocol(IoSession session,byte[] noHandleArray) {

		List<CrcProtocol> crcProtocolList = new ArrayList<>();
		List<SocketModel> socketModelList = new ArrayList<>();

		// 数组处理到的index
		int packetStartIndex = -1;
		int packageNum=0;

		// 检查第一种包头
		for (int i = 0,packetLength=0; i < noHandleArray.length - 1; i=i+packetLength) {

			if (noHandleArray[i] == HEADER1_BYTE1 && noHandleArray[i + 1] == HEADER1_BYTE2) {
				//可以取到长度
				if (i + 3 < noHandleArray.length) {
					byte[] packetLengthByte= {noHandleArray[i + 2],noHandleArray[i + 3]};
					packetLength = ByteUtil.twoBytesToInt(packetLengthByte,0);
					logger.debug("packetLength {}",packetLength);
					if (packetLength>0&&packetLength + i <= noHandleArray.length) {
						byte[] newPackage=ByteUtil.copyOfRange(noHandleArray, i, i + packetLength);
						packageNum=packageNum+1;
						int checkResult=checkNewPackage(newPackage,session);
						if(checkResult!=0)
						{
							packetStartIndex = i + packetLength;
							continue;
						}
						CrcProtocol crcProtocol=new CrcProtocol(newPackage);
						crcProtocolList.add(crcProtocol);
						//取到完整包时，packetStartIndex才改变
						packetStartIndex = i + packetLength;
						continue;
					}
				}
			}

			if (noHandleArray[i] == HEADER2_BYTE1 && noHandleArray[i + 1] == HEADER2_BYTE2) {
				if (i+2 + OLD_PACKET_LENGTH <= noHandleArray.length) {
					packetLength=OLD_PACKET_LENGTH+2;
					byte[] oldPackage=ByteUtil.copyOfRange(noHandleArray, i+2, i +2+ OLD_PACKET_LENGTH);
					packageNum=packageNum+1;
					SocketModel socketModel = new SocketModel(oldPackage);
					socketModel.setBytes(oldPackage);
					socketModelList.add(socketModel);
					//取到完整包时，packetStartIndex才改变
					packetStartIndex = i+2 + OLD_PACKET_LENGTH;
					continue;
				}
			}
			// 如果没有找到符合的包头，就指针偏移到下一位，继续匹配
			packetLength=packetLength+1;
		}

		logger.debug("packetStartIndex {} packageNum {} len {}",packetStartIndex,packageNum,noHandleArray.length);

		// 如果有剩余的字节，保存下来
		byte[] remainingBytes;
		// 如果packetStartIndex不等于-1 说明有整包出现，原来的包要进行截取
		if (packetStartIndex!=-1&&packetStartIndex <noHandleArray.length) {
			remainingBytes = ByteUtil.copyOfRange(noHandleArray, packetStartIndex, noHandleArray.length);
			map.put(session, remainingBytes);
		}
		else if(packetStartIndex >=noHandleArray.length)
		{
			map.put(session, null);
		}
		else
		{
			map.put(session, noHandleArray);
		}


		if(socketModelList.size()>0)
		{
			handleSocketModelList(socketModelList,session);
		}
        if(crcProtocolList.size()>0)
		{
			dispatchService.handleProtocolList(crcProtocolList,session);
		}
	}

    // 新协议包校验
	private int checkNewPackage(byte[] newPackage,IoSession session)
	{
		byte[] returnNoCrcByte;
		byte[] crcByte;
		int result;
        if(newPackage.length<4)
		{
			returnNoCrcByte=ByteUtil.byteMergerAll(newPackage,new byte[]{2});
			crcByte=ByteUtil.calculateCRC(returnNoCrcByte);
			session.write(ByteUtil.byteMergerAll(returnNoCrcByte,crcByte));
			result=1;
			logger.info("sessionId {},len {} short",session.getId(),newPackage.length);
			return result;
		}

		byte[] length=ByteUtil.copyOfRange(newPackage,2,4);
		int len=ByteUtil.twoBytesToInt(length,0);
		if(len<15)
		{
			returnNoCrcByte=ByteUtil.byteMergerAll(newPackage,new byte[]{2});
			crcByte=ByteUtil.calculateCRC(returnNoCrcByte);
			session.write(ByteUtil.byteMergerAll(returnNoCrcByte,crcByte));
			result=2;
			logger.info("sessionId {},len {} short",session.getId(),len);
			return result;
		}
		byte cmd=newPackage[4];
		String cmdStr = ByteConstant.meaningMap.get(cmd);
		byte[] imei=ByteUtil.copyOfRange(newPackage,5,21);
		String imeiStr=ByteUtil.getStringByByteArrays(imei, false);
		byte[] noCrcPackage=ByteUtil.copyOfRange(newPackage,0,len-2);
		byte[] getCrc=ByteUtil.calculateCRC(noCrcPackage);
		byte[] crc=ByteUtil.copyOfRange(newPackage,len-2,len);

		if(getCrc[0]!=crc[0]||getCrc[1]!=crc[1])
		{
			returnNoCrcByte=ByteUtil.byteMergerAll(newPackage,new byte[]{2});
			crcByte=ByteUtil.calculateCRC(returnNoCrcByte);
			session.write(ByteUtil.byteMergerAll(returnNoCrcByte,crcByte));
			result=3;
			logger.info("imei {},cmd {},crc error",imeiStr,cmdStr);
			return result;
		}

		return 0;
	}


	private void handleSocketModelList(List<SocketModel> originalList,IoSession session) {
		// 过滤采集和非采集List
		Map<Boolean, List<SocketModel>> partResultMap =
				originalList.stream().collect(Collectors.partitioningBy(socketModel -> (socketModel.getCmd()[0] == 1||
						socketModel.getCmd()[0] == 102)));

		List<SocketModel> collectList = partResultMap.get(true);
		List<SocketModel> notCollectList = partResultMap.get(false);

		logger.debug("collect len {} not collect len {}", collectList.size(), notCollectList.size());

		if (!collectList.isEmpty()) {
		byte[] pushData=electricalCollectService.batchProcessData(collectList);
		if(pushData!=null)
		{
			mqttService.pushCollectData(pushData);
		}
		}

		if (!notCollectList.isEmpty()) {
			//原来的方法
			for (SocketModel socketModel : notCollectList) {
				handleSocketModel(socketModel, session);
			}
		}

	}

    // 处理老协议的包
	private void handleOldProtocol(IoSession session,byte[] noHandelArray)
	{
		List<SocketModel> originalList = new ArrayList<>();
		while (noHandelArray.length >= 80) {
			// 按80个字节分包，组成对象
			byte[] bytesTemp = Arrays.copyOfRange(noHandelArray, 0, 80);
			SocketModel socketModel = new SocketModel(bytesTemp);
			socketModel.setBytes(bytesTemp);
			originalList.add(socketModel);

			if (noHandelArray.length > 80) {
				// 从原有字节数组中减去80个字节
				noHandelArray = Arrays.copyOfRange(noHandelArray, 80, noHandelArray.length);
			} else {
				// 小于80个字节包将被丢弃 不处理
				noHandelArray = new byte[0];
				map.put(session, null);
			}
		}

		// 如果包小于80字节，就把包存起来
		if (noHandelArray.length != 0) {
			map.put(session, noHandelArray);
		}

		handleSocketModelList(originalList,session);

	}

	// 根据命令字不同，调用不同的处理方法
	private void handleSocketModel(SocketModel socketModel, IoSession session) {
		byte cmd = socketModel.getCmd()[0];
		String meaning = ByteConstant.meaningMap.get(cmd);
		logger.info("cmd {} mean {} {}", cmd, meaning, socketModel);

		byte[] result;
		switch (cmd) {
			case ByteConstant.CMD_01_COLLECT: { //信息收集响应
				componentCollectService.processTheData(socketModel);
				break;
			}
			case ByteConstant.CMD_02_ABNORMAL_ALARM: { // 异常警报
				result = protocolDataService.exceptionWarning(socketModel, session);
				WriteFuture writeFuture = session.write(result);
				if (null != writeFuture.getException()) {
					byte[] tempData = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.DATA_LEN, new byte[]{0x3});
					tempData[4] = socketModel.getData()[0];
					result = ByteUtil.byteMergerAll(tempData, socketModel.getCmd(), socketModel.getChipId(), socketModel.getRsv(), socketModel.getImei());
					session.write(result);
				}
				break;
			}
			case ByteConstant.CMD_03_REMOTE_CONTROL: {
				// 表示远端遥控结果   0x3  手动打开组件会在这里收到消息
				componentService.syncComponentStatus(socketModel);
				mqttService.pushControlRepose(socketModel);
				break;
			}
			case ByteConstant.CMD_04_COMPONENT_AUTH: { // 组件鉴权
				result = protocolDataService.zteComponentAuthResponse(socketModel, session);
				WriteFuture writeFuture = session.write(result);
				if (null != writeFuture.getException()) {
					logger.info("{} auth failed", socketModel.getChipIdString());
					componentService.reSetComponentSessionIdAndIsAuth(session.getId() + "", socketModel.getChipIdString());
					byte[] tempData = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.DATA_LEN, new byte[]{0x3});
					result = ByteUtil.byteMergerAll(tempData, socketModel.getCmd(), socketModel.getChipId(), socketModel.getRsv(), socketModel.getImei());
					session.write(result);
				}
				break;
			}
			case ByteConstant.CMD_08_ABNORMAL_RESTORE: { // 异常解除
				result = protocolDataService.exceptionRemoveWarning(socketModel, session);
				WriteFuture writeFuture = session.write(result);
				if (null != writeFuture.getException()) {
					byte[] tempData = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.DATA_LEN, new byte[]{0x3});
					tempData[4] = socketModel.getData()[0];
					result = ByteUtil.byteMergerAll(tempData, socketModel.getCmd(), socketModel.getChipId(), socketModel.getRsv(), socketModel.getImei());
					session.write(result);
				}
				break;
			}
			case ByteConstant.CMD_09_OPEN_CLOSE: { // 自动打开或关闭上报
				result = protocolDataService.openOrCloseWarning(socketModel, session);
				WriteFuture writeFuture = session.write(result);
				if (null != writeFuture.getException()) {
					byte[] tempData = ByteUtil.loadData(ConstantsExt.ZTEDATANODELEN.DATA_LEN, new byte[]{0x2});
					tempData[4] = socketModel.getData()[0];
					result = ByteUtil.byteMergerAll(tempData, socketModel.getCmd(), socketModel.getChipId(), socketModel.getRsv(), socketModel.getImei());
					session.write(result);
				}
				break;
			}
			// 注册
			case ByteConstant.CMD_100_REGISTER: {
				result = protocolDataService.zteResultResponse(socketModel, session);
				Configure.getMap().put(session.getId() + "", session);
				Configure.getSessionMap().put(socketModel.getImeiString(), session.getId() + "");
				session.write(result);
				logger.info("{} session id is {}",socketModel.getImeiString(),session.getId());
				break;
			}
			case ByteConstant.CMD_101_HEARTBEAT: { // 心跳
				String nowSessionId = session.getId() + "";
				String existSessionId = Configure.getSessionMap().get(socketModel.getImeiString());
				logger.info("now session id {} exist session id {} ",nowSessionId,existSessionId);
				if (existSessionId != null & nowSessionId.equals(existSessionId)) {
					session.write(socketModel.getBytes());
					logger.info("reply heartbeat {}",nowSessionId);
				}
				break;
			}
			// 版本下发申请 待完成
			case ByteConstant.CMD_70_VERSION_APPLY: {
				result = protocolDataService.versionGrantApply(socketModel, session);
				break;
			}
			// 版本下发   FOTA_DATA   组件版本二进制文件,每次下发长度不超过1360字节  待完成
			case ByteConstant.CMD_71_VERSION_SEND: {
				result = protocolDataService.versionGrant(socketModel, session);
				break;
			}
			// 逆变器数据查询 待完成
			case ByteConstant.CMD_90_INVERTER_QUERY: {
				protocolDataService.collectionInverterData(socketModel, session);
				break;
			}
			// 逆变器组串数据查询 待完成
			case ByteConstant.CMD_91_INVERTER_GROUP_QUERY: {
				protocolDataService.collectionInverterGroupData(socketModel, session);
				break;
			}
			// 远端修改参数
			case ByteConstant.CMD_0A_REMOTE_ALTER: {
				result = protocolDataService.warningSetting(socketModel, session);
				break;
			}

			// 中继器，优化器下发版本
			case ByteConstant.CMD_113_RELAY_OPTIMIZER_SEND:
			case ByteConstant.CMD_112_COLLECTOR_UPDATE: { // 采集器版本下发版本
				updateTaskService.handleVersionSentReturn(socketModel);
				break;
			}
			// 加载中继
			case ByteConstant.CMD_49_RELAY_LOAD: {
				updateTaskService.handleVersionLoadReturn(socketModel);
				break;
			}
			// 优化器版本升级请求
			case ByteConstant.CMD_16_OPTIMIZER_UPDATE:
				// 中继器版本升级请求
			case ByteConstant.CMD_48_RELAY_UPDATE: {
				versionService.handleUpdateInfoReturn(socketModel);
				break;
			}
			// 优化器版本版本查询
			case ByteConstant.CMD_18_OPTIMIZER_QUERY:
				// 中继器版本版本查询
			case ByteConstant.CMD_50_RELAY_QUERY:
				// 采集器版本版本查询
			case ByteConstant.CMD_114_COLLECTOR_QUERY: {
				versionService.handleVersionQueryReturn(socketModel);
				break;
			}
			default: {
				logger.info("{} message not handle", socketModel.getChipIdString());
			}
		}
	}
}
