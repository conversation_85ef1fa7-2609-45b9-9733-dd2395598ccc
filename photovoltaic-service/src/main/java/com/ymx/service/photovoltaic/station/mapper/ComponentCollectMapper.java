package com.ymx.service.photovoltaic.station.mapper;

import com.ymx.service.photovoltaic.station.model.ComponentCollect;
import com.ymx.service.photovoltaic.station.model.ComponentElectricModel;
import com.ymx.service.photovoltaic.station.model.ComponentViewModel;
import com.ymx.service.photovoltaic.station.model.ComponentViewModelToVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface ComponentCollectMapper {

	// 创建电站采集表
	String createCollectTable(@Param("powerStationId")String powerStationId);
	// 创建PLC电站采集表
	String createPLCCollectTable(@Param("powerStationId")String powerStationId);
	// 创建电站组串采集表
	String createGroupCollectTable(@Param("powerStationId")String powerStationId);

	// 删除表
	String deleteTable(@Param("powerStationId")String powerStationId);

	// 批量新增采集信息数据
	int saveComponentCollectList(Map<String, Object> map);

	// 批量新增采集信息数据 有两个温度
	int saveNewComponentCollectList(Map<String, Object> map);

	// crc新协议 批量新增采集信息数据 增加了两个温度信息
	int saveCollectList(Map<String, Object> map);

	// 查询采集数据信息
	List<ComponentCollect> queryComponentCollectListByComId(Map<String, Object> map);

	// 查询采集数据信息
	@Deprecated
	Double queryComponentCollectStatistics(Map<String, Object> map);

	// 查询指定 createTime的采集信息
	List<ComponentViewModel> queryPastCollectInfo(Map<String, Object> map);

	// 查询指定 createTime的采集信息
	List<ComponentViewModelToVo> queryPastCollectInfoTo(Map<String, Object> map);

	// 查询最近的采集信息
	List<ComponentViewModel> queryLastCollectInfo(Map<String, Object> map);
	// 查询最近的采集信息
	List<ComponentViewModelToVo> queryLastCollectInfoTo(Map<String, Object> map);


	// 查询最近的采集信息
	List<ComponentElectricModel> queryGroupPastCollectInfo(Map<String, Object> map);
}
