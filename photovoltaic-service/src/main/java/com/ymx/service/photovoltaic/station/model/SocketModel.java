package com.ymx.service.photovoltaic.station.model;

import com.ymx.common.common.constant.ByteConstant;
import com.ymx.common.utils.ByteUtil;
import com.ymx.service.constant.ConstantsExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 封装 Socket
 * <AUTHOR>
 * @version 2018/9/19
 */
public class SocketModel {

	private static final Logger logger = LoggerFactory.getLogger(SocketModel.class);


	public SocketModel() {
	}


	public SocketModel(byte[] bytes) {
		if (null == bytes || 0 == bytes.length) {
			bytes = new byte[ConstantsExt.ZTEDATANODELEN.TOTAL];//或者return
		}
		logger.debug("length {} socketModel data {}", bytes.length, ByteUtil.getStringByByteArrays(bytes, true));
		if (bytes.length < 80) {
			logger.info("data length less than 80");
			return;
		}

		byte[] responseData = ByteUtil.getByteOffset(bytes, 0, 0, 51);
		byte[] responseStatus = ByteUtil.getByteOffset(responseData, 0, 0, 1);

		byte cmd = setBaseValue(bytes);
		String meaning = ByteConstant.meaningMap.get(cmd);
		logger.info("{} {} {}", this.getImeiString(),meaning,this.getChipIdString());


		// 业务处理
		switch (cmd) {
			case ByteConstant.CMD_01_COLLECT: {
				setCollectValue(responseData);
				break;
			}
			case ByteConstant.CMD_102_NEW_COLLECT: {
				setNewCollectValue(responseData);
				break;
			}
			case ByteConstant.CMD_112_COLLECTOR_UPDATE:// 采集器下发版本并升级
			case ByteConstant.CMD_113_RELAY_OPTIMIZER_SEND:// 中继器，优化器下发版本
			case ByteConstant.CMD_114_COLLECTOR_QUERY:// 采集器版本版本查询
			case ByteConstant.CMD_48_RELAY_UPDATE:// 中继器升级命令
			case ByteConstant.CMD_49_RELAY_LOAD:// 中继器固件加载
			case ByteConstant.CMD_50_RELAY_QUERY:// 中继器版本查询
			case ByteConstant.CMD_16_OPTIMIZER_UPDATE: // 优化器升级命令
			case ByteConstant.CMD_18_OPTIMIZER_QUERY: { // 优化器版本查询
				break;
			}
			case ByteConstant.CMD_02_ABNORMAL_ALARM:
			case ByteConstant.CMD_101_HEARTBEAT:// 心跳
			case ByteConstant.CMD_0A_REMOTE_ALTER:// 远端修改参数
			case ByteConstant.CMD_100_REGISTER:// 注册
			case ByteConstant.CMD_03_REMOTE_CONTROL://表示远端遥控结果
			case ByteConstant.CMD_04_COMPONENT_AUTH:// 组件鉴权
			case ByteConstant.CMD_08_ABNORMAL_RESTORE:// 异常解除
			case ByteConstant.CMD_09_OPEN_CLOSE:// 自动打开或关闭上报
			case ByteConstant.CMD_70_VERSION_APPLY:// 版本下发申请
			case ByteConstant.CMD_71_VERSION_SEND: {//// 异常警报
				this.setStatus(Integer.parseInt(ByteUtil.getStringByByteArrays(responseStatus, false)));
				break;
			}
			case ByteConstant.CMD_90_INVERTER_QUERY: { // 逆变器数据查询
				setInverterValue(responseData);
				break;
			}
			case ByteConstant.CMD_91_INVERTER_GROUP_QUERY: { // 逆变器组串数据查询
				setInverterGroupValue(responseData);
				break;
			}
			default: {
				logger.info("socketModel default");
			}
		}
	}

	private byte setBaseValue(byte[] bytes) {
		byte[] dataBytes = ByteUtil.getByteOffset(bytes, ConstantsExt.ZTEDATANODELEN.DATA_INDEX, 0, ConstantsExt.ZTEDATANODELEN.DATA_LEN);//0-50
		/*** 接口控制字 */
		byte[] cmdBytes = ByteUtil.getByteOffset(bytes, ConstantsExt.ZTEDATANODELEN.CMD_INDEX, 0, ConstantsExt.ZTEDATANODELEN.CMD_LEN);//51
		/*** 芯片的ID，唯一标示 */
		byte[] chipIdBytes = ByteUtil.getByteOffset(bytes, ConstantsExt.ZTEDATANODELEN.CHIPID_INDEX, 0, ConstantsExt.ZTEDATANODELEN.CHIPID_LEN);//52-56
		byte[] rsvBytes = ByteUtil.getByteOffset(bytes, ConstantsExt.ZTEDATANODELEN.RSV_INDEX, 0, ConstantsExt.ZTEDATANODELEN.RSV_LEN);//63-57
		/*** 芯片的ID，采集器唯一标示 */
		byte[] imeiBytes = ByteUtil.getByteOffset(bytes, ConstantsExt.ZTEDATANODELEN.IMEI_INDEX, 0, ConstantsExt.ZTEDATANODELEN.IMEI_LEN);//64:79

		// 设置chipId
		if (chipIdBytes[0] == 0 && chipIdBytes[1] == 0 && chipIdBytes[2] <= 1) {
			this.setChipIdString(ByteUtil.getStringByChipIdByteArrays(chipIdBytes, false));
		} else {
			this.setChipIdString(ByteUtil.getHexChipId(chipIdBytes)) ;
		}

		this.setRsvString(ByteUtil.getStringByByteArrays(rsvBytes, false));
		this.setDataString(ByteUtil.getStringByByteArrays(dataBytes, true));
		this.setImeiString(ByteUtil.getStringByByteArrays(imeiBytes, false));

		this.setData(dataBytes);
		this.setChipId(chipIdBytes);
		this.setRsv(rsvBytes);
		this.setImei(imeiBytes);
		this.setCmd(cmdBytes);
		return cmdBytes[0];
	}

	private void setCollectValue(byte[] responseData) {
		byte[] input_voltage = ByteUtil.getByteOffset(responseData, 0, 0, 4);
		// 输入电流
		byte[] input_current = ByteUtil.getByteOffset(responseData, 4, 0, 4);
		// 输出电压
		byte[] output_voltage = ByteUtil.getByteOffset(responseData, 8, 0, 4);
		// 输出电流
		byte[] output_current = ByteUtil.getByteOffset(responseData, 12, 0, 4);
		// 组件温度
		byte[] component_temperature = ByteUtil.getByteOffset(responseData, 16, 0, 4);
		// 优化器的pwm占空比
		byte[] pwm_array = ByteUtil.getByteOffset(responseData, 24, 0, 1);
		// 优化器的通信信号强度指示
		byte[] rssi_array = ByteUtil.getByteOffset(responseData, 25, 0, 1);
		// 优化器的开机计数值，单位是秒
		byte[] tick_array = ByteUtil.getByteOffset(responseData, 26, 0, 2);

		this.setComponentTemperature(ByteUtil.bytesToIntForTemp(component_temperature, 0));
		this.setOutputCurrent(ByteUtil.twoBytesToInt(output_current, 0));
		this.setOutputVoltage(ByteUtil.twoBytesToInt(output_voltage, 0));
		this.setInputCurrent(ByteUtil.twoBytesToInt(input_current, 0));
		this.setInputVoltage(ByteUtil.twoBytesToInt(input_voltage, 0));
		this.setPwm(ByteUtil.oneByteToInt(pwm_array, 0));
		this.setRssi(ByteUtil.oneByteToInt(rssi_array, 0));
		this.setTick(ByteUtil.twoBytesToInt(tick_array, 0));
		byte[] responseStatus2 = ByteUtil.getByteOffset(responseData, 20, 0, 1);
		this.setStatus(Integer.parseInt(ByteUtil.getStringByByteArrays(responseStatus2, false)));
	}


	private void setNewCollectValue(byte[] responseData) {
		byte[] input_voltage = ByteUtil.getByteOffset(responseData, 0, 0, 4);
		// 输入电流
		byte[] input_current = ByteUtil.getByteOffset(responseData, 4, 0, 4);
		// 输出电压
		byte[] output_voltage = ByteUtil.getByteOffset(responseData, 8, 0, 4);
		// 输出电流
		byte[] output_current = ByteUtil.getByteOffset(responseData, 12, 0, 4);
		// pcb管温度
		byte[] component_temperature = ByteUtil.getByteOffset(responseData, 16, 0, 1);
		// mos管温度
		byte[] mos_temperature = ByteUtil.getByteOffset(responseData, 17, 0, 1);
		// 累计发电量
//		byte[] energy = ByteUtil.getByteOffset(responseData, 18, 0, 2);

		// 优化器的pwm占空比
		byte[] pwm_array = ByteUtil.getByteOffset(responseData, 24, 0, 1);
		// 优化器的通信信号强度指示
		byte[] rssi_array = ByteUtil.getByteOffset(responseData, 25, 0, 1);
		// 优化器的开机计数值，单位是秒
		byte[] tick_array = ByteUtil.getByteOffset(responseData, 26, 0, 2);

		this.setComponentTemperature(ByteUtil.OneByteForTemp(component_temperature, 0));
		this.setMosTemperature(ByteUtil.OneByteForTemp(mos_temperature, 0));
		this.setOutputCurrent(ByteUtil.twoBytesToInt(output_current, 0));
		this.setOutputVoltage(ByteUtil.twoBytesToInt(output_voltage, 0));
		this.setInputCurrent(ByteUtil.twoBytesToInt(input_current, 0));
		this.setInputVoltage(ByteUtil.twoBytesToInt(input_voltage, 0));
		this.setPwm(ByteUtil.oneByteToInt(pwm_array, 0));
		this.setRssi(ByteUtil.oneByteToInt(rssi_array, 0));
		this.setTick(ByteUtil.twoBytesToInt(tick_array, 0));
		byte[] responseStatus2 = ByteUtil.getByteOffset(responseData, 20, 0, 1);
		this.setStatus(Integer.parseInt(ByteUtil.getStringByByteArrays(responseStatus2, false)));
	}

	private void setInverterGroupValue(byte[] responseData) {
		List<InverterCollectInfo> list = new ArrayList<>();
		for (int i = 0; i < 8; i++) {
			// 输出电压
			byte[] groupId = ByteUtil.getByteOffset(responseData, i * 6, 0, 2);
			// 输出电流
			byte[] inputCurrent = ByteUtil.getByteOffset(responseData, (i * 6 + 2), 0, 2);
			// 输出电压
			byte[] inputVoltage = ByteUtil.getByteOffset(responseData, (i * 6 + 4), 0, 2);
			int inputCurrentInt = ByteUtil.bytesToInt(inputCurrent, 0);
			int inputVoltageInt = ByteUtil.bytesToInt(inputVoltage, 0);
			if (inputCurrentInt > 0 || inputVoltageInt > 0) {
				InverterCollectInfo inverterCollectInfo = new InverterCollectInfo();
				inverterCollectInfo.setChipId(ByteUtil.bytesToInt(groupId, 0) + "");
				inverterCollectInfo.setInputCurrent(inputCurrentInt);
				inverterCollectInfo.setInputVoltage(inputVoltageInt);
				list.add(inverterCollectInfo);
			}
		}
		this.setCollectInfoList(list);
	}

	private void setInverterValue(byte[] responseData) {
		// 输出电压
		byte[] output_voltageA = ByteUtil.getByteOffset(responseData, 0, 0, 2);
		// 输出电流
		byte[] output_currentA = ByteUtil.getByteOffset(responseData, 2, 0, 2);
		// 输出电压
		byte[] output_voltageB = ByteUtil.getByteOffset(responseData, 4, 0, 2);
		// 输出电流
		byte[] output_currentB = ByteUtil.getByteOffset(responseData, 6, 0, 2);
		// 输出电压
		byte[] output_voltageC = ByteUtil.getByteOffset(responseData, 8, 0, 2);
		// 输出电流
		byte[] output_currentC = ByteUtil.getByteOffset(responseData, 10, 0, 2);

		//今日发电量
		byte[] electricDay = ByteUtil.getByteOffset(responseData, 12, 0, 2);
		// 总发电量
		byte[] electricTotal = ByteUtil.getByteOffset(responseData, 14, 0, 2);
		// 组件温度
		byte[] component_temperature = ByteUtil.getByteOffset(responseData, 16, 0, 2);

		byte[] responseStatus2 = ByteUtil.getByteOffset(responseData, 18, 0, 2);
		// 当前错误码
		byte[] errorCode = ByteUtil.getByteOffset(responseData, 20, 0, 2);
		// 当前保护码
		byte[] protectCode = ByteUtil.getByteOffset(responseData, 22, 0, 2);

		this.setComponentTemperature(ByteUtil.bytesToInt(component_temperature, 0));
		this.setOutputCurrent(ByteUtil.bytesToInt(output_currentA, 0));
		this.setOutputVoltage(ByteUtil.bytesToInt(output_voltageA, 0));
		this.setOutputCurrentB(ByteUtil.bytesToInt(output_currentB, 0));
		this.setOutputVoltageB(ByteUtil.bytesToInt(output_voltageB, 0));
		this.setOutputCurrentC(ByteUtil.bytesToInt(output_currentC, 0));
		this.setOutputVoltageC(ByteUtil.bytesToInt(output_voltageC, 0));
		this.setElectricDay(ByteUtil.bytesToInt(electricDay, 0));
		this.setElectricTotal(ByteUtil.bytesToInt(electricTotal, 0));
		this.setErrorCode(ByteUtil.bytesToInt(errorCode, 0));
		this.setProtectCode(ByteUtil.bytesToInt(protectCode, 0));

		this.setStatus(Integer.parseInt(ByteUtil.getStringByByteArrays(responseStatus2, false)));

	}


	/**
	 * 原始的80个字节
	 */
	private byte[] bytes;

	private byte[] imei;
	private byte[] rsv;
	private byte[] chipId;
	private byte[] data;

	private String imeiString;
	/**
	 * 远程控制 第一位 1单个， 2批量      第二位 1打开 2关闭
	 * 批量定时查询  唯一码（批次号）
	 */

	private String rsvString;
	private String chipIdString;
	private byte[] cmd;
	private String dataString;
	// 组件温度
	private Integer componentTemperature;
	// 组件温度
	private Integer mosTemperature;
	// 输出电流
	private Integer outputCurrent;
	// 输出电压
	private Integer outputVoltage;
	// 输入电流
	private Integer inputCurrent;
	// 输入电压
	private Integer inputVoltage;
	private Integer status;
	private Integer type;

	// 输出电流
	private Integer outputCurrentB;
	// 输出电压
	private Integer outputVoltageB;
	// 输出电流
	private Integer outputCurrentC;
	// 输出电压
	private Integer outputVoltageC;
	//总发电量
	private Integer electricTotal;
	//今日发电量
	private Integer electricDay;
	// 当前错误码
	private Integer errorCode;
	//	当前保护码
	private Integer protectCode;

	// 优化器的pwm占空比
	private Integer pwm;
	// 优化器的通信信号强度指示
	private Integer rssi;
	// 优化器的开机计数值，单位是秒
	private Integer tick;

	private List<InverterCollectInfo> collectInfoList = new ArrayList<>();

	public byte[] getBytes() {
		return bytes;
	}

	public void setBytes(byte[] bytes) {
		this.bytes = bytes;
	}

	public byte[] getImei() {
		return imei;
	}

	public void setImei(byte[] imei) {
		this.imei = imei;
	}

	public byte[] getRsv() {
		return rsv;
	}

	public void setRsv(byte[] rsv) {
		this.rsv = rsv;
	}

	public byte[] getChipId() {
		return chipId;
	}

	public void setChipId(byte[] chipId) {
		this.chipId = chipId;
	}

	public String getImeiString() {
		return imeiString;
	}

	public void setImeiString(String imeiString) {
		this.imeiString = imeiString;
	}

	public String getRsvString() {
		return rsvString;
	}

	public void setRsvString(String rsvString) {
		this.rsvString = rsvString;
	}

	public String getChipIdString() {
		return chipIdString;
	}

	public void setChipIdString(String chipIdString) {
		this.chipIdString = chipIdString;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}


	public byte[] getCmd() {
		return cmd;
	}

	public void setCmd(byte[] cmd) {
		this.cmd = cmd;
	}

	public byte[] getData() {
		return data;
	}

	public void setData(byte[] data) {
		this.data = data;
	}

	public String getDataString() {
		return dataString;
	}

	public void setDataString(String dataString) {
		this.dataString = dataString;
	}

	public Integer getComponentTemperature() {
		return componentTemperature;
	}

	public void setComponentTemperature(Integer componentTemperature) {
		this.componentTemperature = componentTemperature;
	}

	public Integer getMosTemperature() {
		return mosTemperature;
	}

	public void setMosTemperature(Integer mosTemperature) {
		this.mosTemperature = mosTemperature;
	}

	public Integer getOutputCurrent() {
		return outputCurrent;
	}

	public void setOutputCurrent(Integer outputCurrent) {
		this.outputCurrent = outputCurrent;
	}

	public Integer getOutputVoltage() {
		return outputVoltage;
	}

	public void setOutputVoltage(Integer outputVoltage) {
		this.outputVoltage = outputVoltage;
	}

	public Integer getInputCurrent() {
		return inputCurrent;
	}

	public void setInputCurrent(Integer inputCurrent) {
		this.inputCurrent = inputCurrent;
	}

	public Integer getInputVoltage() {
		return inputVoltage;
	}

	public void setInputVoltage(Integer inputVoltage) {
		this.inputVoltage = inputVoltage;
	}

	public Integer getOutputCurrentB() {
		return outputCurrentB;
	}

	public void setOutputCurrentB(Integer outputCurrentB) {
		this.outputCurrentB = outputCurrentB;
	}

	public Integer getOutputVoltageB() {
		return outputVoltageB;
	}

	public void setOutputVoltageB(Integer outputVoltageB) {
		this.outputVoltageB = outputVoltageB;
	}

	public Integer getOutputCurrentC() {
		return outputCurrentC;
	}

	public void setOutputCurrentC(Integer outputCurrentC) {
		this.outputCurrentC = outputCurrentC;
	}

	public Integer getOutputVoltageC() {
		return outputVoltageC;
	}

	public void setOutputVoltageC(Integer outputVoltageC) {
		this.outputVoltageC = outputVoltageC;
	}

	public Integer getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(Integer errorCode) {
		this.errorCode = errorCode;
	}

	public Integer getProtectCode() {
		return protectCode;
	}

	public void setProtectCode(Integer protectCode) {
		this.protectCode = protectCode;
	}

	public Integer getElectricTotal() {
		return electricTotal;
	}

	public void setElectricTotal(Integer electricTotal) {
		this.electricTotal = electricTotal;
	}

	public Integer getElectricDay() {
		return electricDay;
	}

	public void setElectricDay(Integer electricDay) {
		this.electricDay = electricDay;
	}

	public List<InverterCollectInfo> getCollectInfoList() {
		return collectInfoList;
	}

	public void setCollectInfoList(List<InverterCollectInfo> collectInfoList) {
		this.collectInfoList = collectInfoList;
	}


	public Integer getPwm() {
		return pwm;
	}

	public void setPwm(Integer pwm) {
		this.pwm = pwm;
	}

	public Integer getRssi() {
		return rssi;
	}

	public void setRssi(Integer rssi) {
		this.rssi = rssi;
	}

	public Integer getTick() {
		return tick;
	}

	public void setTick(Integer tick) {
		this.tick = tick;
	}

	@Override
	public String toString() {
		String out = "";
		if (cmd[0] == 0x1) {
			out = ", \n componentTemperature=" + componentTemperature +
					",  outputCurrent=" + outputCurrent +
					",  outputVoltage=" + outputVoltage +
					",  inputCurrent=" + inputCurrent +
					",  inputVoltage=" + inputVoltage +
					",  pwm=" + pwm +
					",  rssi=" + rssi +
					",  tick=" + tick;
		}
		return "SocketModel{  " +
				"imei='" + imeiString + '\'' +
				",   rsv='" + rsvString + '\'' +
				",   chipId='" + chipIdString + '\'' +
				",   cmd=" + cmd[0] +
				",   status='" + status + '\'' +
				out +
				", \n data='" + dataString + '\'' +
				'}';
	}

}
