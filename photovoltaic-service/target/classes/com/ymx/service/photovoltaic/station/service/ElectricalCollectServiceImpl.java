package com.ymx.service.photovoltaic.station.service;

import com.ymx.common.utils.ByteUtil;
import com.ymx.common.utils.DateUtils;
import com.ymx.service.cache.Configure;
import com.ymx.service.photovoltaic.station.mapper.ComponentCollectMapper;
import com.ymx.service.photovoltaic.station.mapper.ComponentMapper;
import com.ymx.service.photovoltaic.station.model.ComponentCollect;
import com.ymx.service.photovoltaic.station.model.ComponentModel;
import com.ymx.service.photovoltaic.station.model.SendCollectModel;
import com.ymx.service.photovoltaic.station.model.SocketModel;
import com.ymx.service.third.jedis.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ElectricalCollectServiceImpl implements ElectricalCollectService{

    private static final Logger logger = LoggerFactory.getLogger(ElectricalCollectServiceImpl.class);
    private final SimpleDateFormat hourFormat = new SimpleDateFormat("HH");
    private final SimpleDateFormat minuteFormat = new SimpleDateFormat("mm");
    private final SimpleDateFormat secondFormat = new SimpleDateFormat("ss");



    @Autowired
    private ComponentMapper componentMapper;

    @Autowired
    private ComponentCollectMapper componentCollectMapper;



    @Override
    public byte[] batchProcessData(List<SocketModel> socketModelList) {
        {
            // 得到chipIdList
            List<String> chipIdList = Optional.of(socketModelList).
                    orElse(new ArrayList<>()).stream().
                    map(SocketModel::getChipIdString).collect(Collectors.toList());

            Map<String ,Object> map = new HashMap<>();
            String imei=socketModelList.get(0).getImeiString();
            map.put("imei", imei);
            map.put("list", chipIdList);
            // 查询得到ComponentModel list  确认传过来的数据在组件表中是否存在
            // 电站组件数据放入缓存中  循环时进行判断
            List<ComponentModel> componentModelList=componentMapper.getComponentList(map);

            // 不存在 直接返回 不做入库操作
            if (componentModelList.isEmpty()) {
                return null;
            }

            // key chipId value groupId  组件 组串id 对应map
            Map<String,String> chipGroupMap=componentModelList.stream().
                    collect(Collectors.toMap(ComponentModel::getChipId,ComponentModel::getBelongsGroupId));

            // 组件采集list
            List<ComponentCollect> componentCollectList=new ArrayList<>();

            Date nowDate=new Date();
            // 循环设置ComponentCollect的值 并放入相应的集合中
            for (SocketModel socketModel: socketModelList ) {
                // socketModel转为 componentCollect对象
                ComponentCollect componentCollect = new ComponentCollect(socketModel);
                // 如果组件不在组件表中 查询不到组串id
                String groupId=chipGroupMap.get(componentCollect.getChipId());
                // 如果组件不在组件表中或者不属于这个采集器，则不能入库
                if(groupId==null)
                {
                    logger.info("{} groupId null",componentCollect.getChipId());
                    continue;
                }

                componentCollect.setGroupId(groupId);
                String rsvStr=socketModel.getRsvString();
                componentCollect.setCreateTime(nowDate);
                // rsvStr为0000000 表示定时上报，采集时间和创建时间保存为一样的
                if(rsvStr.equals("0000000"))
                {
                    componentCollect.setCollectTime(nowDate);
                }
                else
                {
                    // rsv不用保存，日志里有记录
                    String batchNo= RedisUtil.getRedisDateTime(socketModel.getRsvString(),groupId);
                    componentCollect.setCollectTime(DateUtils.parse(batchNo,DateUtils.DATE_All_KEY_STR));
                }

                // componentCollect加入list
                componentCollectList.add(componentCollect);
            }

            //采集数据批量入库
            if(!componentCollectList.isEmpty())
            {

                saveComponentCollectList(componentCollectList,componentModelList.get(0).getPowerStationId(), socketModelList.get(0).getCmd()[0] != 1);
            }

            // 按组串id对采集list分组
            Map<String, List<ComponentCollect>> groupListMap = componentCollectList.stream().
                    collect(Collectors.groupingBy(ComponentCollect::getGroupId, Collectors.toList()));

            // key groupId  value 组串采集list
            Map<String, List<ComponentCollect>> coverMap= Configure.getCoverMap();
            groupListMap.forEach((groupId,collectList)->
            {
                List<ComponentCollect> beforeCollectList=coverMap.get(groupId);
                if(beforeCollectList!=null)
                {
                    beforeCollectList.addAll(collectList);
                }else
                {
                    beforeCollectList=collectList;
                }
                coverMap.put(groupId,beforeCollectList);
            });

            // 没有入库的也发送了，这个要改一下，最前面加一下总长度
            SendCollectModel sendCollectModel= Configure.imeiPowerMap.get(imei);
            if(sendCollectModel!=null&&sendCollectModel.getIsPush()==1)
            {
                // 先过滤出入库的chipIdList 然后再过滤出入库的socketModelList
                List<String> okChipIdList=componentCollectList.stream().map(ComponentCollect::getChipId).
                        collect(Collectors.toList());
                List<SocketModel> okSocketModelList=socketModelList.stream().
                        filter(socketModel -> okChipIdList.contains(socketModel.getChipIdString())).
                        collect(Collectors.toList());
                return getPushData(okSocketModelList,componentCollectList.get(0).getCollectTime());
            }
        }

        return null;

    }



    private byte[] getPushData(List<SocketModel> socketModelList, Date collectTime)
    {
        byte[] allChip=null;
        for (SocketModel s:socketModelList
        ) {
            byte[] chipId=s.getChipId();
            byte[] data=s.getData();
            byte[] input_voltage = ByteUtil.getByteOffset(data, 0, 0, 4);
            // 输入电流
            byte[] input_current = ByteUtil.getByteOffset(data, 4, 0, 4);
            // 输出电压
            byte[] output_voltage = ByteUtil.getByteOffset(data, 8, 0, 4);
            // 输出电流
            byte[] output_current = ByteUtil.getByteOffset(data, 12, 0, 4);
            // 组件温度
            byte[] component_temperature = ByteUtil.getByteOffset(data, 16, 0, 4);
            byte[] oneChip=ByteUtil.byteMergerAll(chipId,input_current,input_voltage,
                    output_current,output_voltage,component_temperature);
            if (allChip == null) {
                allChip = oneChip;
            } else {
                allChip = ByteUtil.byteMergerAll(allChip, oneChip);
            }
        }
        if(allChip!=null)
        {
            int len=allChip.length+5;
            String hour = hourFormat.format(collectTime);
            String min = minuteFormat.format(collectTime);
            String second = secondFormat.format(collectTime);
            byte[] hourArray=ByteUtil.int2Bytes(Integer.parseInt(hour),1);
            byte[] minArray=ByteUtil.int2Bytes(Integer.parseInt(min),1);
            byte[] secondArray=ByteUtil.int2Bytes(Integer.parseInt(second),1);

            byte[] lenArray=ByteUtil.int2Bytes(len,2);
            allChip=ByteUtil.byteMergerAll(lenArray,hourArray,minArray,secondArray,allChip);
        }
        return allChip;
    }



    public int saveComponentCollectList(List<ComponentCollect> list, String powerStationId,Boolean isNewCollect) {
        Map<String, Object> map = new HashMap<>();
        map.put("list",list);
        map.put("tableName","_"+powerStationId);
        if(isNewCollect)
        {
            return componentCollectMapper.saveNewComponentCollectList(map);
        }
        else return componentCollectMapper.saveComponentCollectList(map);
    }
}
